<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Account;
use Stripe\AccountLink;
use App\Models\User;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;
use Stripe\StripeClient;

class StripeConnectController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }

    public function connectStripe()
    {
        $user = Auth::user();
        try {
            if ($user->stripe_enabled) {
                return redirect()->back()->with(['type' => 'info', 'message' => 'Your Stripe account is already connected.', 'title' => 'Connected Status']);
            }
            if (!$user->stripe_account_id) {
                $account = Account::create([
                    'type' => 'express',
                    'email' => $user->email,
                    'capabilities' => [
                        'transfers' => ['requested' => true],
                    ],
                ]);
                $user->stripe_account_id = $account->id;
                $user->save();
            }
            $accountLink = AccountLink::create([
                'account' => $user->stripe_account_id,
                'refresh_url' => config('services.stripe.refresh_url'),
                'return_url' => config('services.stripe.redirect_url'),
                'type' => 'account_onboarding',
            ]);
            return redirect($accountLink->url);
        } catch (\Exception $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Stripe connection failed: ' . $e->getMessage(), 'title' => 'Error']);
        }
    }

    public function handleStripeReturn()
    {
        return redirect()->route('dashboard')->with(['type' => 'success', 'message' => 'Onboarding completed. Your Stripe status will update shortly.', 'title' => 'Success']);
    }

    public function disconnectStripe()
    {
        $user = Auth::user();
        if (!$user->stripe_account_id) {
            return redirect()->back()->with(['type' => 'info', 'message' => 'You are not connected to Stripe.', 'title' => 'Not Connected']);
        }
        try {
            $user->stripe_account_id = null;
            $user->stripe_enabled = false;
            $user->save();

            return redirect()->back()->with(['type' => 'success', 'message' => 'Stripe account disconnected.', 'title' => 'Disconnected']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Failed to disconnect: ' . $e->getMessage(), 'title' => 'Error']);
        }
    }

    public function handle(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');

    $connectedSecret = env('STRIPE_CONNECT_WEBHOOK');    
        $disconnectedSecret = env('STRIPE_DISCONNECT_WEBHOOK');
        $event = null;
        try {
            $event = Webhook::constructEvent($payload, $sig_header, $connectedSecret);
        } catch (\UnexpectedValueException $e) {
            return response('Invalid payload', 400);
        } catch (SignatureVerificationException $e) {
            try {
                $event = Webhook::constructEvent($payload, $sig_header, $disconnectedSecret);
            } catch (SignatureVerificationException $e) {
                return response('Invalid signature', 400);
            }
        }
        if ($event->type === 'account.updated') {
            $account = $event->data->object;
            $user = User::where('stripe_account_id', $account->id)->first();
            if ($user) {
                $user->stripe_enabled = $account->charges_enabled && $account->payouts_enabled;
                $user->save();
            }
        }
        if ($event->type === 'account.application.deauthorized') {
            $accountId = $event->account;
            $user = User::where('stripe_account_id', $accountId)->first();
            if ($user) {
                $user->stripe_account_id = null;
                $user->stripe_enabled = false;
                $user->save();
            }
        }
        return response('Webhook handled', 200);
    }
}
