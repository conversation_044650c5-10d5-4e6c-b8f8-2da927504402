@extends('dashboard.layout.master')
@push('css')
    <style>
        /* Make validation errors red and bold */
        .error {
            color: #dc3545 !important;
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Social Platforms</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('social-platforms-create')
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-social-platform">
                            <i class="fa-solid fa-plus me-3"></i> Add Social Platform
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="SearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="SearchInput"
                                    placeholder="Search Platforms..." />
                            </div>
                        </div>
                        <div class="row row-gap-5 mt-5" id="socialPlatformCardsContainer">
                            @include('dashboard.admin.social_platforms.partials.social-platform-cards', [
                                'platforms' => $platforms,
                            ])
                        </div>
                        <div class="row mt-10" id="loadMoreContainer"
                            style="{{ ($totalCount ?? 0) <= 10 ? 'display: none;' : '' }}">
                            <div class="col-12 text-center">
                                <button type="button" class=" add-btn" id="loadMoreBtn">
                                    Load More
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.social_platforms.modal.add-social-platform-modal')
    @include('dashboard.admin.social_platforms.modal.edit-social-platform-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            let currentOffset = 10;
            let isLoading = false;
            let currentSearch = '';
            let searchTimeout;

            // Add custom validation method for file size
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true;
                }
                const fileSizeKB = element.files[0].size / 1024;
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            // Add form validation
            $("#socialPlatformForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    name: {
                        required: true
                    },
                    avatar: {
                        required: true,
                        maxFileSize: 5120
                    },
                },
                messages: {
                    name: {
                        required: "Please enter platform name"
                    },
                    avatar: {
                        required: "Please upload an image",
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Edit form validation
            $("#editSocialPlatformForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    name: {
                        required: true
                    },
                    avatar: {
                        required: false,
                        maxFileSize: 5120
                    },
                },
                messages: {
                    name: {
                        required: "Please enter platform name"
                    },
                    avatar: {
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                },
                submitHandler: function(form) {
                    var platformId = $('#socialPlatformId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/social-platforms/' + platformId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-social-platform').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            var response = xhr.responseJSON;
                            if (response && response.message) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message
                                });
                            } else {
                                alert('Update failed. Please try again.');
                            }
                        }
                    });
                },
            });


            $('#SearchInput').on('keyup', function() {
                let search = $(this).val().trim();
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    currentSearch = search;
                    currentOffset = 0;
                    searchSocialPlatforms();
                }, 300);
            });

            function searchSocialPlatforms() {
                if (isLoading) return;
                isLoading = true;
                let data = {
                    offset: currentOffset
                };

                if (currentSearch) {
                    data.search = currentSearch;
                }
                console.log('Search request data:', data);
                $.ajax({
                    url: "{{ route('social-platforms.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            $('#socialPlatformCardsContainer').html(response.html);
                            currentOffset = response.next_offset;
                            updateLoadMoreButton(response);
                            bindEditButtons();
                            if (data.search) {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'error');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            $('#loadMoreBtn').on('click', function() {
                console.log('Load more clicked, current offset:', currentOffset);
                loadMoreSocialPlatforms();
            });

            function loadMoreSocialPlatforms() {
                if (isLoading) return;
                isLoading = true;
                let data = {
                    offset: currentOffset
                };
                if (currentSearch) {
                    data.search = currentSearch;
                }
                $.ajax({
                    url: "{{ route('social-platforms.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            $('#socialPlatformCardsContainer').append(response.html);
                            currentOffset = response.next_offset;
                            updateLoadMoreButton(response);
                            bindEditButtons();
                        }
                    },
                    error: function(xhr, status, error) {
                        showToast('Failed to load more records. Please try again.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            function updateLoadMoreButton(response) {
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            function bindEditButtons() {
                $('.edit-social-platform').off('click').on('click', function(e) {
                    e.preventDefault();
                    var platformId = $(this).data('id');
                    $.ajax({
                        url: '/admin/social-platforms/' + platformId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#socialPlatformId').val(data.id);
                            $('#edit-name').val(data.name);
                            if (data.image) {
                                var baseImageUrl = $('meta[name="asset-url"]').attr(
                                    'content') ||
                                    '/website';
                                var imageUrl = baseImageUrl + '/' + data.image;
                                var imageInput = $(
                                    '#edit-social-platform .image-input[data-kt-image-input="true"]'
                                    );
                                var wrapper = imageInput.find('.image-input-wrapper');
                                wrapper.css('background-image', 'url(' + imageUrl + ')');
                                imageInput.removeClass('image-input-empty').addClass(
                                    'image-input-changed');
                                imageInput.find('[data-kt-image-input-action="remove"]')
                                    .removeClass('d-none');
                                imageInput.find('[data-kt-image-input-action="cancel"]')
                                    .removeClass('d-none');
                            }
                            $('#edit-social-platform').modal('show');
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching platform data:', error);
                            alert('Error loading data. Please try again.');
                        }
                    });
                });
            }
            bindEditButtons();

            // Function to show toast notifications
            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : type === 'warning' ? 'bg-warning' :
                    'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    autohide: true,
                    delay: 3000
                });
                bsToast.show();
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }
        });
    </script>
@endpush
