@extends('dashboard.layout.master')
@push('css')
    <style>
        .nav-pills .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        .nav-pills .nav-link {
            color: #007bff;
            border-radius: 0.375rem;
        }
        .table-container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 1.5rem;
        }
        .add-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            color: white;
        }
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        .role-admin { background-color: #dc3545; color: white; }
        .role-developer { background-color: #6f42c1; color: white; }
        .role-business { background-color: #fd7e14; color: white; }
        .role-individual { background-color: #20c997; color: white; }
        .role-professional { background-color: #0dcaf0; color: white; }
        .role-customer { background-color: #198754; color: white; }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Navigation Links Management</h6>
                        <p class="fs-14 sora light-black m-0">Manage navigation links for different user roles</p>
                    </div>
                    <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-navigation-link">
                        <i class="fa-solid fa-plus me-3"></i> Add Navigation Link
                    </a>
                </div>

                <div class="col-lg-12">
                    <!-- Role Tabs -->
                    <ul class="nav nav-pills mb-4" id="roleTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="admin-tab" data-bs-toggle="pill"
                                data-bs-target="#admin-panel" type="button" role="tab"
                                aria-controls="admin-panel" aria-selected="true">
                                <i class="fas fa-user-shield me-2"></i>Admin
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="developer-tab" data-bs-toggle="pill"
                                data-bs-target="#developer-panel" type="button" role="tab"
                                aria-controls="developer-panel" aria-selected="false">
                                <i class="fas fa-code me-2"></i>Developer
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="business-tab" data-bs-toggle="pill"
                                data-bs-target="#business-panel" type="button" role="tab"
                                aria-controls="business-panel" aria-selected="false">
                                <i class="fas fa-building me-2"></i>Business
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="individual-tab" data-bs-toggle="pill"
                                data-bs-target="#individual-panel" type="button" role="tab"
                                aria-controls="individual-panel" aria-selected="false">
                                <i class="fas fa-user me-2"></i>Individual
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="professional-tab" data-bs-toggle="pill"
                                data-bs-target="#professional-panel" type="button" role="tab"
                                aria-controls="professional-panel" aria-selected="false">
                                <i class="fas fa-user-tie me-2"></i>Professional
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="customer-tab" data-bs-toggle="pill"
                                data-bs-target="#customer-panel" type="button" role="tab"
                                aria-controls="customer-panel" aria-selected="false">
                                <i class="fas fa-users me-2"></i>Customer
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="roleTabContent">
                        <!-- Admin Panel -->
                        <div class="tab-pane fade show active" id="admin-panel" role="tabpanel" aria-labelledby="admin-tab">
                            <div class="table-container">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h6 class="sora black mb-0">Admin Navigation Links (Sidebar)</h6>
                                    <span class="role-badge role-admin">Admin Role</span>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Title</th>
                                                <th>Route</th>
                                                <th>Show in Menu</th>
                                                <th>Show in Footer</th>
                                                <th>Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="admin-links">
                                            @forelse($navigationLinks->where('roles', 'admin') as $link)
                                                <tr>
                                                    <td>{{ $link->title }}</td>
                                                    <td><code>{{ $link->route }}</code></td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_menu ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_menu ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_footer ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_footer ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $link->order }}</td>
                                                    <td>
                                                        <span class="badge {{ $link->status ? 'bg-success' : 'bg-danger' }}">
                                                            {{ $link->status ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-2">
                                                            <button class="btn btn-sm btn-outline-primary edit-link"
                                                                data-id="{{ $link->id }}" data-bs-toggle="modal"
                                                                data-bs-target="#edit-navigation-link">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger delete-link"
                                                                data-id="{{ $link->id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="d-flex flex-column align-items-center">
                                                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                                                            <h5 class="text-muted">No Navigation Links Found</h5>
                                                            <p class="text-muted">Start by adding your first navigation link for admin role</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Developer Panel -->
                        <div class="tab-pane fade" id="developer-panel" role="tabpanel" aria-labelledby="developer-tab">
                            <div class="table-container">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h6 class="sora black mb-0">Developer Navigation Links (Sidebar)</h6>
                                    <span class="role-badge role-developer">Developer Role</span>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Title</th>
                                                <th>Route</th>
                                                <th>Show in Menu</th>
                                                <th>Show in Footer</th>
                                                <th>Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="developer-links">
                                            @forelse($navigationLinks->where('roles', 'developer') as $link)
                                                <tr>
                                                    <td>{{ $link->title }}</td>
                                                    <td><code>{{ $link->route }}</code></td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_menu ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_menu ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_footer ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_footer ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $link->order }}</td>
                                                    <td>
                                                        <span class="badge {{ $link->status ? 'bg-success' : 'bg-danger' }}">
                                                            {{ $link->status ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-2">
                                                            <button class="btn btn-sm btn-outline-primary edit-link"
                                                                data-id="{{ $link->id }}" data-bs-toggle="modal"
                                                                data-bs-target="#edit-navigation-link">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger delete-link"
                                                                data-id="{{ $link->id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="d-flex flex-column align-items-center">
                                                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                                                            <h5 class="text-muted">No Navigation Links Found</h5>
                                                            <p class="text-muted">Start by adding your first navigation link for developer role</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Business Panel -->
                        <div class="tab-pane fade" id="business-panel" role="tabpanel" aria-labelledby="business-tab">
                            <div class="table-container">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h6 class="sora black mb-0">Business Navigation Links (Top Bar)</h6>
                                    <span class="role-badge role-business">Business Role</span>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Title</th>
                                                <th>Route</th>
                                                <th>Show in Menu</th>
                                                <th>Show in Footer</th>
                                                <th>Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="business-links">
                                            @forelse($navigationLinks->where('roles', 'business') as $link)
                                                <tr>
                                                    <td>{{ $link->title }}</td>
                                                    <td><code>{{ $link->route }}</code></td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_menu ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_menu ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $link->show_in_footer ? 'bg-success' : 'bg-secondary' }}">
                                                            {{ $link->show_in_footer ? 'Yes' : 'No' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $link->order }}</td>
                                                    <td>
                                                        <span class="badge {{ $link->status ? 'bg-success' : 'bg-danger' }}">
                                                            {{ $link->status ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-2">
                                                            <button class="btn btn-sm btn-outline-primary edit-link"
                                                                data-id="{{ $link->id }}" data-bs-toggle="modal"
                                                                data-bs-target="#edit-navigation-link">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger delete-link"
                                                                data-id="{{ $link->id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="d-flex flex-column align-items-center">
                                                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                                                            <h5 class="text-muted">No Navigation Links Found</h5>
                                                            <p class="text-muted">Start by adding your first navigation link for business role</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>