<?php $__env->startPush('css'); ?>
    <link href="assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <style>
        .image-input-placeholder {
            background-image: url("<?php echo e(asset('website/assets/media/avatars')); ?>/avatar.svg");
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                    Settings
                </h1>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<div id="" class="app-content flex-column-fluid mb-20">
    <div id="kt_app_content_container" class="app-container container-xxl">
        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo e($error); ?> <br>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
        <div class="card">
            <div class="card-body">
                <form method="post" id="kt_modal_update_role_form"
                    action="<?php echo e(route('settings.update', $setting->id)); ?>" class="form-horizontal"
                    enctype="multipart/form-data">
                    <?php echo e(method_field('PATCH')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="row g-7">
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Title</span>
                            </label>
                            <input type="text" name="title" class="form-control form-control-solid" placeholder=""
                                value="<?php echo e($setting->title ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="required">Description</span>
                            </label>
                            <textarea class="form-control form-control-solid" rows="3" name="description" placeholder="Type Target Details"><?php echo e($setting->description ?? ''); ?></textarea>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Favicon</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('website') . '/' . $setting->favicon ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="favicon" value="<?php echo e($setting->favicon ?? ''); ?>"
                                            accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Logo</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('website') . '/' . $setting->logo ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="logo" value="<?php echo e($setting->logo ?? ''); ?>"
                                            accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Loader Logo</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('website') . '/' . $setting->loader_logo ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="loader_logo"
                                            value="<?php echo e($setting->loaderlogo ?? ''); ?>" accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Footer_text</span>
                            </label>
                            <input type="text" name="footer_text" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->footer_text ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Phone</span>
                            </label>
                            <input type="text" name="phone" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->phone ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Email</span>
                            </label>
                            <input type="text" name="email" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->email ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Facebook
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                fill="currentColor" />
                                            <path
                                                d="M13.643 9.36206C13.6427 9.05034 13.7663 8.75122 13.9864 8.53052C14.2065 8.30982 14.5053 8.18559 14.817 8.18506H15.992V5.23999H13.643C13.1796 5.24052 12.7209 5.33229 12.293 5.51013C11.8651 5.68798 11.4764 5.94841 11.1491 6.27649C10.8219 6.60457 10.5625 6.99389 10.3857 7.42224C10.209 7.85059 10.1183 8.30956 10.119 8.77295V11.718H7.769V14.663H10.119V21.817C11.2812 22.0479 12.4762 22.0604 13.643 21.854V14.663H15.992L17.167 11.718H13.643V9.36206Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="facebook" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->facebook ?? ''); ?>" />
                        </div>
                        
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Youtube
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M21 6.30005C20.5 5.30005 19.9 5.19998 18.7 5.09998C17.5 4.99998 14.5 5 11.9 5C9.29999 5 6.29998 4.99998 5.09998 5.09998C3.89998 5.19998 3.29999 5.30005 2.79999 6.30005C2.19999 7.30005 2 8.90002 2 11.9C2 14.8 2.29999 16.5 2.79999 17.5C3.29999 18.5 3.89998 18.6001 5.09998 18.7001C6.29998 18.8001 9.29999 18.8 11.9 18.8C14.5 18.8 17.5 18.8001 18.7 18.7001C19.9 18.6001 20.5 18.4 21 17.5C21.6 16.5 21.8 14.9 21.8 11.9C21.8 9.00002 21.5 7.30005 21 6.30005ZM9.89999 15.7001V8.20007L14.5 11C15.3 11.5 15.3 12.5 14.5 13L9.89999 15.7001Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="youtube" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->youtube ?? ''); ?>" />
                        </div>
                        
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Whatsapp</span>
                            </label>
                            <input type="text" name="whatsapp" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->whatsapp ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Header Image</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('website') . '/' . $setting->header_image ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="header_image"
                                            value="<?php echo e($setting->header_image ?? ''); ?>" accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Header Text</span>
                            </label>
                            <input type="text" name="header_text" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->header_text ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Stripe Publishable Key</span>
                            </label>
                            <input type="text" name="stripe_publishable" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->stripe_publishable ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Stripe Secret Key</span>
                            </label>
                            <input type="text" name="stripe_secret" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->stripe_secret ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Stripe Webhook Key</span>
                            </label>
                            <input type="text" name="stripe_webhook" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->stripe_webhook ?? ''); ?>" />
                        </div>

                        <!--begin::Card footer-->
                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            
                            <button type="submit" class="add-btn" id="kt_project_settings_submit">
                                Update
                            </button>
                        </div>
                        <!--end::Card footer-->
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    var hostUrl = "assets/";
</script>
<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
<!--end::Global Javascript Bundle-->
<!--begin::Vendors Javascript(used for this page only)-->
<script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/datatables/datatables.bundle.js"></script>
<!--end::Vendors Javascript-->
<!--begin::Custom Javascript(used for this page only)-->
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/apps/projects/settings/settings.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/widgets.bundle.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/widgets.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/upgrade-plan.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/create-app.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/users-search.js"></script>
<script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/new-target.js"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/settings/edit.blade.php ENDPATH**/ ?>