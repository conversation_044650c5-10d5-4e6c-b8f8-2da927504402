<div class="fixed-theme-header">
    <div class="discount-header text-white">
        <div class="container d-flex justify-content-between align-items-center py-5">
            <img src="<?php echo e(asset('website').'/'.setting()->header_image ?? ''); ?>" alt="Logo">
            <p class="m-0 fs-13 nunito-sans regular">
                <?php echo e(setting()->header_text ?? ''); ?>

            </p>
            <div class="d-flex gap-4">
                <a href="mailto:<?php echo e(setting()->email ?? ''); ?>" class="fs-13 text-white regular">
                    <i class="fa-solid fa-envelope me-2"></i> <?php echo e(setting()->email ?? ''); ?>

                </a>
                <a href="tel:<?php echo e(setting()->phone ?? ''); ?>" class="fs-13 text-white regular">
                    <i class="fa-solid fa-phone me-2"></i> <?php echo e(setting()->phone ?? ''); ?>

                </a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2 w-400px align-items-center">
                <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    <img src="<?php echo e(asset('website') . '/' . setting()->logo ?? ''); ?>" alt="Logo"
                        class="h-35px w-30px">
                    <!-- <img src="<?php echo e(asset('website')); ?>/assets/images/logo-image.svg" alt="Logo" class="h-45px w-45px"> -->
                </a>

                <!-- Search Bar -->
                <form class="d-flex w-100 form-control rounded-pill align-items-center header-search h-50">
                    <i class="fa-solid fa-magnifying-glass me-3"></i>
                    <label for="searchInputToday" class="visually-hidden">Services Lookin for today</label>
                    <input class="w-100 fs-14 normal sora" type="search"
                        placeholder="What service are you looking for today?" name="searchInputToday"
                        id="searchInputToday" aria-label="Search Today">
                </form>
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4" id="mainNavbar">
                <?php if(auth()->guard()->check()): ?>
                    <div class="app-navbar-item ms-1 ">
                        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px"
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                            <i class="far fa-bell"></i>
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            </i>
                        </div>
                        <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                            data-kt-menu="true" id="kt_menu_notifications">

                            <p class="fs-16 bold">Notifications</p>

                            <?php for($i = 0; $i < 4; $i++): ?>
                                <div
                                    class="d-flex align-items-center gap-3 justify-content-center  border-bottom mb-5 pb-3">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/notification-user.png" alt="Logo"
                                        class="h-50px">
                                    <div>
                                        <p class="fs-13 mb-0 light-black"><span class="semi_bold ">Jenny Wilson</span> Lorem
                                            Ipsum is simply dummy text of the printing.</p>
                                        <p class="fs-12 mb-0 ">5 min</p>
                                    </div>
                                </div>
                            <?php endfor; ?>

                            <a href="<?php echo e(route('notification')); ?>" class="see-all-btn"> See All</a>
                        </div>
                    </div>
                    <?php if(auth()->user()->hasRole('customer')): ?>
                        <div lass="app-navbar-item ms-1">
                            <i class="far fa-envelope mt-1"></i>
                        </div>

                        <div lass="app-navbar-item ms-1">
                            <i class="far fa-question-circle mt-1"></i>
                        </div>
                        <div>
                            <a href="<?php echo e(route('favorite_professional')); ?>"
                                class="<?php if(request()->is('favorite_professional')): ?> active-fav <?php endif; ?>" aria-label="Favourite">
                                <i class="fa-regular fa-heart"></i>
                            </a>
                        </div>
                        <div>
                            <a href="<?php echo e(route('cart')); ?>" aria-label="Add to Cart">
                                <!-- <i class="fa-solid fa-cart-shopping"></i> -->
                                <i class="bi bi-cart3"></i>
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                        <div class="cursor-pointer symbol symbol-35px "
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end">
                            <?php if(!auth()->user()->profile || auth()->user()->profile->pic == null): ?>
                                <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png" class="rounded-pill"
                                    alt="user" />
                            <?php else: ?>
                                <img alt="Logo" src="<?php echo e(asset('website') . '/' . auth()->user()->profile->pic); ?>" />
                            <?php endif; ?>
                        </div>

                        <div class="menu menu-sub right-sidebar-menus menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                            data-kt-menu="true">
                            <div class="menu-item px-3">
                                <div class="menu-content d-flex align-items-center px-3">
                                    <div class="symbol symbol-50px me-5">
                                        <?php if(!auth()->user()->profile || auth()->user()->profile->pic == null): ?>
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png"
                                                class="rounded-pill" alt="user" />
                                        <?php else: ?>
                                            <img alt="Logo"
                                                src="<?php echo e(asset('website') . '/' . auth()->user()->profile->pic); ?>" />
                                        <?php endif; ?>
                                    </div>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold d-flex align-items-center fs-5"><?php echo e(Auth::user()->name ?? ''); ?>

                                        </div>
                                        <a href="#"
                                            class="fw-semibold deep-blue fs-7"><?php echo e(Auth::user()->email ?? ''); ?></a>
                                    </div>
                                </div>
                            </div>
                            <div class="separator my-2"></div>
                            <?php if(auth()->check() &&
                                    auth()->user()->hasAnyRole(['individual', 'business', 'admin'])): ?>
                                <div class="menu-item px-3">
                                    <a href="<?php echo e(route('dashboard')); ?>" class="menu-link px-5">Dashboard</a>
                                </div>
                            <?php endif; ?>
                            <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                                <div class="menu-item px-3">
                                    <a href="<?php echo e(route('profile_setting')); ?>" class="menu-link px-5">Profile</a>
                                </div>
                            <?php else: ?>
                                <div class="menu-item px-3">
                                    <a href="<?php echo e(route('profile_settings')); ?>" class="menu-link px-5">Profile</a>
                                </div>
                            <?php endif; ?>

                            <div class="separator my-2"></div>
                            <div class="menu-item px-3">
                                <a href="<?php echo e(url('logout')); ?>" class="menu-link px-5 logout">Logout</a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="<?php echo e(url('register')); ?>" class="button button1 login-btn">Register / Login</a>
                <?php endif; ?>

            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-7">
                <li class="nav-item"><a
                        class="nav-link fs-14 noraml sora semi_bold header-active    <?php if(request()->is('/')): ?> active <?php endif; ?>"
                        href="<?php echo e(url('/')); ?>">Home</a></li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 semi_bold sora header-active  <?php if(request()->is('services')): ?> active <?php endif; ?>"
                        href="#!" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="servicesLink">
                        Services
                    </a>
                    <div class="dropdown-menu service-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        
                        <?php if (isset($component)) { $__componentOriginaled6aa9fb32da68a613649e141abdc9b4 = $component; } ?>
<?php $component = App\View\Components\NavbarServiceComponent::resolve(['route' => 'website_services','type' => 'service'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbar-service-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\NavbarServiceComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled6aa9fb32da68a613649e141abdc9b4)): ?>
<?php $component = $__componentOriginaled6aa9fb32da68a613649e141abdc9b4; ?>
<?php unset($__componentOriginaled6aa9fb32da68a613649e141abdc9b4); ?>
<?php endif; ?>
                        
                    </div>
                </li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 header-active  semi_bold sora  <?php if(request()->is('professional')): ?> active <?php endif; ?>"
                        href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="professionalLink">
                        Professional
                    </a>
                    <div class="dropdown-menu professional-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        <?php if (isset($component)) { $__componentOriginaled6aa9fb32da68a613649e141abdc9b4 = $component; } ?>
<?php $component = App\View\Components\NavbarServiceComponent::resolve(['route' => 'professional','type' => 'professional'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbar-service-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\NavbarServiceComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled6aa9fb32da68a613649e141abdc9b4)): ?>
<?php $component = $__componentOriginaled6aa9fb32da68a613649e141abdc9b4; ?>
<?php unset($__componentOriginaled6aa9fb32da68a613649e141abdc9b4); ?>
<?php endif; ?>
                        
                    </div>
                </li>

                <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold <?php if(request()->is('friends.index')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('friends.index')); ?>">Friends</a></li>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold <?php if(request()->is('customer_booking')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('customer_booking')); ?>">My Booking</a></li>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold <?php if(request()->is('customer_wallet')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('customer_wallet')); ?>">Wallet</a></li>
                <?php endif; ?>
                <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item ms-auto"><a class="nav-link blue-text fs-14 sora semi_bold pe-0"
                            href="<?php echo e(url('register')); ?>">Become a professional →</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/website/template/header.blade.php ENDPATH**/ ?>