
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <h1>Create New Page</h1>
            <form action="<?php echo e(route('cms.home.edit')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <div class="col-md-12">
                                <div class="sub-sections-container">
                                    <div class="section-block mb-10 card-box">
                                        <div class="col-md-12 mb-4">
                                            <label for="kicker" class="fieldlabels w-100">Title(Page Name in
                                                Navigation)</label>
                                            <input type="text" class="form-control" name="title"
                                                placeholder="Enter kicker text" />
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="heading_one" class="fieldlabels w-100">Page Heading</label>
                                            <input type="text" class="form-control" name="heading"
                                                placeholder="Enter first heading" />
                                        </div>


                                        <div class="col-md-12 mb-4">
                                            <label for="description" class="fieldlabels w-100">Description</label>
                                            <textarea class="form-control" name="description" id="description" rows="4" placeholder="Enter description"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="blue-btn">Create</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="https://cdn.ckeditor.com/ckeditor5/41.2.1/classic/ckeditor.js"></script>
<script>
    ClassicEditor
        .create(document.querySelector('#description'))
        .catch(error => {
            console.error(error);
        });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/create.blade.php ENDPATH**/ ?>