
<?php $__env->startPush('css'); ?>
    <style>
        .page-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .form-inputs-field {
            border-radius: 10px;
            border: 1px solid var(--input-border, #e1e5e9);
            padding: 14px 16px;
            background: var(--white, #fff);
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-inputs-field:focus {
            border-color: var(--deep-blue, #007bff);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .form-input-labels {
            color: var(--black, #000);
            font-family: Sora, sans-serif;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .add-btn {
            cursor: pointer;
            border: 0;
            padding: 12px 24px;
            font-family: Inter, sans-serif;
            color: var(--white, #fff);
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            background: var(--deep-blue, #007bff);
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .add-btn:hover {
            background: var(--deep-blue-hover, #0056b3);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
            color: white;
        }
        .cancel-btn {
            cursor: pointer;
            border: 1px solid #6c757d;
            padding: 12px 24px;
            font-family: Inter, sans-serif;
            color: #6c757d;
            font-size: 16px;
            font-weight: 500;
            border-radius: 8px;
            background: transparent;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .cancel-btn:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-1px);
        }
        .page-header {
            margin-bottom: 2rem;
        }
        .breadcrumb-item {
            color: #6c757d;
            font-size: 14px;
        }
        .breadcrumb-item.active {
            color: var(--deep-blue, #007bff);
            font-weight: 600;
        }
        .required {
            color: #dc3545;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <!-- Page Header -->
                <div class="col-md-12">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="sora black mb-2">Edit Page</h6>
                                <p class="fs-14 sora light-black m-0">Edit your CMS page</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Card -->
                <div class="col-md-12">
                    <div class="page-card">
                        <form action="<?php echo e(route('cms.update', $page->id)); ?>" method="POST" enctype="multipart/form-data" id="cmsupdateForm">
                            <?php echo csrf_field(); ?>
                            <div class="row row-gap-4">
                                <!-- Page Title -->
                                <div class="col-md-12">
                                    <label for="title" class="form-label form-input-labels">
                                        Page Title <span class="required">*</span>
                                        <small class="text-muted d-block">(This will appear in navigation)</small>
                                    </label>
                                    <input type="text"
                                           class="form-control form-inputs-field"
                                           name="title"
                                           id="title"
                                           placeholder="Enter page title (e.g., About Us, Contact)"
                                            value="<?php echo e($page->title ?? ''); ?>"/>
                                    <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger small"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Page Heading -->
                                <div class="col-md-12">
                                    <label for="heading" class="form-label form-input-labels">
                                        Page Heading <span class="required">*</span>
                                        <small class="text-muted d-block">(Main heading displayed on the page)</small>
                                    </label>
                                    <input type="text"
                                           class="form-control form-inputs-field"
                                           name="heading"
                                           id="heading"
                                           placeholder="Enter page heading" value="<?php echo e($page->heading ?? ''); ?>"/>
                                    <?php $__errorArgs = ['heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger small"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Page Description -->
                                <div class="col-md-12">
                                    <label for="description" class="form-label form-input-labels">
                                        Page Content <span class="required">*</span>
                                        <small class="text-muted d-block">(Main content of the page)</small>
                                    </label>
                                    <textarea class="form-control form-inputs-field" name="description" id="description" rows="6"
                                              placeholder="Enter page content..."><?php echo e(old('description')); ?><?php echo e($page->description ?? ''); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger small"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Action Buttons -->
                                <div class="col-md-12">
                                    <div class="d-flex gap-3 justify-content-end">
                                        <a href="<?php echo e(url()->previous()); ?>" class="cancel-btn">
                                            <i class="fas fa-times"></i>
                                            Cancel
                                        </a>
                                        <button type="submit" class="add-btn">
                                            <i class="fas fa-plus"></i>
                                            Update Page
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.ckeditor.com/ckeditor5/41.2.1/classic/ckeditor.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize CKEditor
            let editorInstance;
            ClassicEditor
                .create(document.querySelector('#description'), {
                    toolbar: [
                        'heading', '|',
                        'bold', 'italic', 'underline', '|',
                        'bulletedList', 'numberedList', '|',
                        'outdent', 'indent', '|',
                        'blockQuote', 'insertTable', '|',
                        'undo', 'redo'
                    ],
                    placeholder: 'Enter your page content here...'
                })
                .then(editor => {
                    editorInstance = editor;

                    // Update textarea when editor content changes
                    editor.model.document.on('change:data', () => {
                        document.querySelector('#description').value = editor.getData();
                    });
                })
                .catch(error => {
                    console.error('CKEditor initialization error:', error);
                });

            // Form validation
            $("#cmsupdateForm").validate({
                errorClass: "text-danger small",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    title: {
                        required: true,
                        maxlength: 100,
                        minlength: 3
                    },
                    heading: {
                        required: true,
                        maxlength: 200,
                        minlength: 3
                    },
                    description: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    title: {
                        required: "Page title is required",
                        maxlength: "Page title cannot exceed 100 characters",
                        minlength: "Page title must be at least 3 characters"
                    },
                    heading: {
                        required: "Page heading is required",
                        maxlength: "Page heading cannot exceed 200 characters",
                        minlength: "Page heading must be at least 3 characters"
                    },
                    description: {
                        required: "Page content is required",
                        minlength: "Page content must be at least 10 characters"
                    }
                },
                submitHandler: function(form) {
                    // Update textarea with CKEditor content before submission
                    if (editorInstance) {
                        document.querySelector('#description').value = editorInstance.getData();
                    }

                    // Show loading state
                    const submitBtn = $(form).find('button[type="submit"]');
                    const originalText = submitBtn.html();
                    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Creating...').prop('disabled', true);

                    // Submit form
                    form.submit();
                }
            });

            // Character counter for inputs
            function addCharacterCounter(selector, maxLength) {
                $(selector).on('input', function() {
                    const current = $(this).val().length;
                    const remaining = maxLength - current;
                    let counterElement = $(this).siblings('.char-counter');

                    if (counterElement.length === 0) {
                        counterElement = $('<small class="char-counter text-muted"></small>');
                        $(this).after(counterElement);
                    }

                    counterElement.text(`${current}/${maxLength} characters`);

                    if (remaining < 10) {
                        counterElement.removeClass('text-muted').addClass('text-warning');
                    } else if (remaining < 0) {
                        counterElement.removeClass('text-warning').addClass('text-danger');
                    } else {
                        counterElement.removeClass('text-warning text-danger').addClass('text-muted');
                    }
                });
            }

            // Add character counters
            addCharacterCounter('#title', 100);
            addCharacterCounter('#heading', 200);
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/show.blade.php ENDPATH**/ ?>