@extends('dashboard.layout.master')
@push('css')
    <style>
        /* Make validation errors red and bold */
        .error {
            color: #dc3545 !important;
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Countries</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('certifications-create')
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-certification">
                            <i class="fa-solid fa-plus me-3"></i> Add Country
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="countrySearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="countrySearchInput"
                                    placeholder="Search countries..." />
                            </div>
                        </div>
                        <div class="row row-gap-5 mt-5" id="countryCardsContainer">
                            @include('dashboard.admin.countries.partials.country-cards', ['countries' => $countries])
                        </div>
                        <div class="row mt-10" id="loadMoreContainer" style="{{ ($totalCount ?? 0) <= 10 ? 'display: none;' : '' }}">
                            <div class="col-12 text-center">
                                <button type="button" class=" add-btn" id="loadMoreBtn">
                                    Load More
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.countries.modal.add-countries-modal')
    @include('dashboard.admin.countries.modal.edit-countries-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            let currentOffset = 10; // Start from 10 since first 10 are already loaded
            let isLoading = false;
            let currentSearch = ''; // Store current search term
            let searchTimeout; // Store search timeout for debounce

            // Add form validation
            $("#countriesForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    name: {
                        required: true
                    },
                    country_code: {
                        required: true
                    },
                },
                messages: {
                    name: {
                        required: "Please enter country name"
                    },
                    country_code: {
                        required: "Please enter country code"
                    },
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Edit form validation
            $("#editCountriesForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    name: {
                        required: true
                    },
                    country_code: {
                        required: true
                    },
                },
                messages: {
                    name: {
                        required: "Please enter country name"
                    },
                    country_code: {
                        required: "Please enter country code"
                    },
                },
                submitHandler: function(form) {
                    var countryId = $('#countryId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/countries/' + countryId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-countries').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                },
            });

            // Search functionality with debounce
            $('#countrySearchInput').on('keyup', function() {
                let search = $(this).val().trim();
                console.log('Country search input changed:', search);

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounce (300ms delay)
                searchTimeout = setTimeout(function() {
                    currentSearch = search;
                    currentOffset = 0; // Reset offset for search
                    searchCountries(); // Search function
                }, 300);
            });

            // Function to search country records
            function searchCountries() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object - only include non-empty values
                let data = {
                    offset: currentOffset
                };

                if (currentSearch) {
                    data.search = currentSearch;
                }

                console.log('Search request data:', data);

                $.ajax({
                    url: "{{ route('countries.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        console.log('Country search response:', response);
                        if (response.success) {
                            // Replace cards content for search
                            $('#countryCardsContainer').html(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();

                            console.log('Country cards updated with', response.count, 'records');

                            // Show success toast with result count only when searching
                            if (data.search) {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'error');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Country search error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Load more functionality
            $('#loadMoreBtn').on('click', function() {
                console.log('Load more clicked, current offset:', currentOffset);
                loadMoreCountries();
            });

            // Function to load more country records
            function loadMoreCountries() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object
                let data = {
                    offset: currentOffset
                };

                if (currentSearch) {
                    data.search = currentSearch;
                }

                console.log('Load more request data:', data);

                $.ajax({
                    url: "{{ route('countries.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        console.log('Load more response:', response);
                        if (response.success) {
                            // Append content for load more
                            $('#countryCardsContainer').append(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();

                            console.log('Country cards appended with', response.count, 'more records');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load more error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Failed to load more records. Please try again.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Function to update load more button visibility
            function updateLoadMoreButton(response) {


                // Show/hide load more button based on whether there are more records
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            // Function to bind edit buttons (needed after AJAX content load)
            function bindEditButtons() {
                $('.edit-country').off('click').on('click', function(e) {
                    e.preventDefault();
                    var countryId = $(this).data('id');
                    $.ajax({
                        url: '/admin/countries/' + countryId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#countryId').val(data.id);
                            $('#edit-name').val(data.name);
                            $('#edit-country-code').val(data.country_code);
                            $('#edit-countries').modal('show');
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching country data:', error);
                            alert('Error loading data. Please try again.');
                        }
                    });
                });
            }

            // Initial binding of edit buttons
            bindEditButtons();

            // Function to show toast notifications
            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : type === 'warning' ? 'bg-warning' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                // Append toast to body
                $('body').append(toast);

                // Get the toast element
                const toastElement = $('.toast').last();

                // Initialize and show the toast
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    autohide: true,
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

        });
    </script>


@endpush

