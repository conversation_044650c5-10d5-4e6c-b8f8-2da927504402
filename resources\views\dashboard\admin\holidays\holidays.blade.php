@extends('dashboard.layout.master')
@push('css')
    <style>
        /* Make validation errors red and bold */
        .error {
            color: #dc3545 !important;
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service manage-pro-holidays">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Manage Holidays</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <div class="d-flex gap-2">
                        @can('holidays-create')
                            <a class="add-btn" data-bs-toggle="modal" data-bs-target="#import-holidays">
                                <i class="fa-solid fa-upload me-3"></i> Import Holidays
                            </a>
                            <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-holiday">
                                <i class="fa-solid fa-plus me-3"></i> Add Holidays
                            </a>
                        @endcan
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">

                        <table id="responsiveTable" class="manage-holiday vat-managment-table display w-100">
                            <thead>
                                <tr>
                                    <th>EVENT TITLE</th>
                                    <th>DATE</th>
                                    <th>ACTION</th>
                                </tr>
                            </thead>
                            <tbody id="holidayTableBody">
                                @include('dashboard.admin.holidays.partials.holiday-table', [
                                    'holidays' => $holidays,
                                ])
                            </tbody>
                        </table>
                        <!-- Load More Button -->
                        <div class="text-center mt-4" id="loadMoreContainer"
                            style="{{ $totalCount > 10 ? '' : 'display: none;' }}">
                            <button type="button" class="btn add-btn" id="loadMoreBtn">
                                <i class="fas fa-plus me-2"></i>Load More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.holidays.modal.add-holiday-modal')
    @include('dashboard.admin.holidays.modal.edit-holiday-modal')
    @include('dashboard.admin.holidays.modal.import-holidays-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            let currentOffset = 10; // Start from 10 since first 10 are already loaded
            let isLoading = false;
            // Add custom regex validation method (if not already included)
            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");
            // Form validation
            $("#holidayForm").validate({
                rules: {
                    name: {
                        required: true,
                        pattern: /^[a-zA-Z0-9\s]+$/,
                        maxlength: 30
                    },
                    date: {
                        required: true
                    },
                    country_id: {
                        required: true
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    name: {
                        required: "Please enter holiday name",
                        pattern: "Only letters, numbers, and spaces are allowed.",
                        maxlength: "Name is too long."
                    },
                    date: {
                        required: "Please select date"
                    },
                    country_id: {
                        required: "Please select country"
                    },
                    status: {
                        required: "Please select status"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Load more functionality
            $('#loadMoreBtn').on('click', function() {
                loadMoreHolidays();
            });

            // Function to load more holiday records
            function loadMoreHolidays() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object
                let data = {
                    offset: currentOffset
                };

                $.ajax({
                    url: "{{ route('holidays.load-more') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            // Append content for load more
                            $('#holidayTableBody').append(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load more error:', error);
                        console.error('Response:', xhr.responseText);
                        alert('Failed to load more records. Please try again.');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Function to update load more button visibility
            function updateLoadMoreButton(response) {
                // Show/hide load more button based on whether there are more records
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            // Function to bind edit buttons (needed after AJAX content load)
            function bindEditButtons() {
                $('.edit-holiday').off('click').on('click', function(e) {
                    e.preventDefault();
                    var holidayId = $(this).data('id');
                    $.ajax({
                        url: '/admin/holidays/' + holidayId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#holidayId').val(data.ids);
                            $('#edit_name').val(data.name);
                            $('#edit_date').val(data.date);

                            // Set and trigger change for Select2 dropdowns
                            $('#edit_country_id').val(data.country_id).trigger('change');
                            $('#edit_status').val(data.status).trigger('change');
                        },
                        complete: function() {
                            $('#edit-holiday').modal('show');
                        },
                        error: function(xhr) {
                            alert('Failed to fetch holiday data. Please try again.');
                        }
                    });
                });
            }

            // Add custom regex validation method (if not already included)
            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");
            // Edit form validation
            $("#editHolidayForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    name: {
                        required: true,
                        pattern: /^[a-zA-Z0-9\s]+$/,
                        maxlength: 30
                    },
                    country_id: {
                        required: true
                    },
                    date: {
                        required: true
                    },
                },
                messages: {
                    name: {
                        required: "Please enter holiday name",
                        pattern: "Only letters, numbers, and spaces are allowed.",
                        maxlength: "Name is too long."
                    },
                    country_id: {
                        required: "Please select a country"
                    },
                    date: {
                        required: "Please select a date"
                    },
                },
                submitHandler: function(form) {
                    var holidayId = $('#holidayId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/holidays/' + holidayId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-holiday').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                },
            });

            // Import form validation
            $("#importHolidaysForm").validate({
                errorClass: "error",
                errorElement: "span",
                rules: {
                    import_file: {
                        required: true
                    },
                    country_id: {
                        required: true
                    },
                },
                messages: {
                    import_file: {
                        required: "Please select a file to import"
                    },
                    country_id: {
                        required: "Please select a country"
                    },
                },
                submitHandler: function(form) {
                    // Let the AJAX handler in the script section handle the submission
                    $(form).trigger('submit.import');
                },
            });

            // Initialize Select2 for import modal when it's shown
            $('#import-holidays').on('shown.bs.modal', function () {
                $('#import_country_id').select2({
                    dropdownParent: $('#import-holidays'),
                    placeholder: 'Select Country',
                    allowClear: false
                });
            });

            // Clean up Select2 when modal is hidden
            $('#import-holidays').on('hidden.bs.modal', function () {
                $('#import_country_id').select2('destroy');
            });

            // Initial binding of edit buttons
            bindEditButtons();
        });
    </script>

    <script>
        // Handle import form submission
        $('#importHolidaysForm').on('submit.import', function(e) {
            e.preventDefault();

            // Check if form is valid
            if (!$(this).valid()) {
                return false;
            }

            var formData = new FormData(this);
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();

            // Show loading state
            submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i>Importing...');
            submitBtn.prop('disabled', true);

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#import-holidays').modal('hide');
                    Swal.fire({
                        icon: response.type || 'success',
                        title: response.title || 'Success',
                        html: response.message || 'Import completed successfully'
                    }).then((result) => {
                        // Reload page when user clicks OK
                        location.reload();
                    });
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    var errorMessage = 'Import failed. Please try again.';

                    if (response && response.message) {
                        errorMessage = response.message;
                    } else if (xhr.status === 422 && response && response.errors) {
                        // Handle validation errors
                        var errors = [];
                        for (var field in response.errors) {
                            errors = errors.concat(response.errors[field]);
                        }
                        errorMessage = errors.join('<br>');
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Import Failed',
                        html: errorMessage
                    });
                },
                complete: function() {
                    // Reset button state
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }
            });
        });
    </script>
@endpush
