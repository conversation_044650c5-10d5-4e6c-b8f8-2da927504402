<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserGallery;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CustomersExport;
use App\Models\Certification;
use App\Models\Holiday;
use App\Models\Service;
use App\Models\UserCertificate;
use App\Models\UserIntroCard;
use App\Models\UserOpeningHour;
use App\Models\UserHoliday;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    function index()
    {
        $data = [];
        if (auth()->user()->hasRole('admin')) {
            $data["totalCustomers"] = User::whereHas('roles', function ($q) {
                $q->where('name', 'customer');
            })->count();
            $data["totalProfessionals"] = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['individual', 'business']);
            })->count();
            $data["totalBookings"] = 0;
            $data["totalRevenue"] = 0;
            $data["professionals"] = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['individual', 'business']);
            })->take(6)->get();
            $data["customers"] = User::whereHas('roles', function ($q) {
                $q->where('name', 'customer');
            })->take(6)->get();
        }
        return view('dashboard.index', $data);
    }
    public function adminCustomers(Request $request)
    {
        $customers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->with(['profile', 'roles'])->paginate(10);
        // if($request->ajax()){
        //     return view('dashboard.admin.customers.partials.customers-table', compact('customers'))->render();
        // }
        return view('dashboard.admin.customers.customers', compact('customers'));
    }

    function changeStatus($id)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->where('ids', $id)->firstOrFail();

        if ($user->status == 1) {
            $user->status = 0;
            $message = 'Customer deactivated successfully';
        } else {
            $user->status = 1;
            $message = 'Customer activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => $message, 'type' => 'success']);
    }

    /**
     * Export customers to Excel
     */
    public function exportCustomers()
    {
        $customers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->with(['profile'])->get();

        return Excel::download(new CustomersExport($customers), 'customers_' . date('Y-m-d') . '.xlsx');
    }

    public function profileSetting()
    {
        $user = auth()->user();
        $services = Service::where('status', 1)->get();
        $product_certifications = Certification::all();
        $holidays = Holiday::all();
        return view('dashboard.profile_settings.index', compact('user', 'services', 'product_certifications', 'holidays'));
    }

    public function saveGalleries(Request $request)
    {
        $request->validate([
            'images.*' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'old_images.*' => 'nullable|string',
        ]);
        $user = auth()->user();

        $imagesToDelete = [];

        // Collect old images that will be replaced by new uploads
        if ($request->has('galleries')) {
            foreach ($request->galleries as $gallery) {
                if (isset($gallery['image']) && isset($gallery['old_image'])) {
                    // New image is being uploaded to replace old one
                    $imagesToDelete[] = $gallery['old_image'];
                }
            }
        }

        // Delete only the images that are being replaced
        foreach ($imagesToDelete as $imageToDelete) {
            $this->deleteImage($imageToDelete);
        }

        // Delete all existing gallery entries
        UserGallery::where('user_id', $user->id)->delete();

        // Save new galleries
        if ($request->has('galleries')) {
            foreach ($request->galleries as $gallery) {
                $userGallery = new UserGallery();
                $userGallery->user_id = $user->id;
                if (isset($gallery['image'])) {
                    $userGallery->image = $this->storeImage('user-gallery', $gallery['image']);
                } elseif (isset($gallery['old_image'])) {
                    $userGallery->image = $gallery['old_image'];
                }
                $userGallery->save();
            }
        }
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'User galleries updated successfully!',
            'type' => 'success'
        ]);
    }

    public function updateCompanyDetails(Request $request)
    {
        $user = auth()->user();

        $user->profile->company_name = $request->company_name;
        $user->profile->vat_number = $request->vat_number;
        $user->profile->company_id = $request->company_id;
        $user->profile->save();
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Company details updated successfully!',
            'type' => 'success'
        ]);
    }

    public function updateProductCertifications(Request $request)
    {
        $request->validate([
            'product_certifications' => 'nullable|array',
            'product_certifications.*' => 'exists:certifications,id',
        ]);

        $user = auth()->user();
        if ($request->has('product_certifications')) {
            $user->product_cerficates()->sync($request->product_certifications);
        }
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Product certifications updated successfully!',
            'type' => 'success'
        ]);
    }

    public function saveCertificates(Request $request)
    {
        $user = auth()->user();
        UserCertificate::where('user_id', $user->id)->delete();
        if ($request->has('certificates')) {
            UserCertificate::where('user_id', $user->id)->delete();
            foreach ($request->certificates as $certification) {
                $userCertificate = new UserCertificate();
                $userCertificate->user_id = $user->id;
                $userCertificate->title = $certification['title'] ?? null;
                $userCertificate->issued_by = $certification['issued_by'] ?? null;
                $userCertificate->issued_date = $certification['issued_date'] ?? null;
                $userCertificate->end_date = $certification['end_date'] ?? null;
                if (isset($certification['image'])) {
                    $userCertificate->image = $this->storeImage('certificates', $certification['image']);
                } elseif (isset($certification['old_image'])) {
                    $userCertificate->image = $certification['old_image'];
                }
                $userCertificate->exception = $certification['exception'] ?? null;
                $userCertificate->exception_reason = $certification['exception_reason'] ?? null;
                $userCertificate->save();
            }
        }
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Certificates updated successfully!',
            'type' => 'success'
        ]);
    }

    public function saveAvailability(Request $request)
    {
        $user = auth()->user();
        // Validate the request
        $request->validate([
            'availability' => 'required|array',
            'holidays' => 'nullable|array',
            'custom_holidays' => 'nullable|array',
        ]);

        $opening_hours = $request->availability ?? [];
        UserOpeningHour::where('user_id', $user->id)->delete();
        foreach ($opening_hours as $hour) {
            $userOpeningHour = new UserOpeningHour();
            $userOpeningHour->user_id = $user->id;
            $userOpeningHour->day = $hour['day'];
            $userOpeningHour->open = $hour['start'] ?? null;
            $userOpeningHour->close = $hour['end'] ?? null;
            if ($hour['start'] && $hour['end']) {
                $userOpeningHour->type = "open";
            } else {
                $userOpeningHour->type = "close";
            }
            $userOpeningHour->save();
        }

        // Handle holidays
        $holidays = $request->holidays ?? [];
        if (!empty($holidays)) {
            UserHoliday::where('user_id', $user->id)->where('is_custom', 0)->delete();
            foreach ($holidays as $holiday) {
                if (isset($holiday['holiday_id'])) {
                    $exist_holiday = Holiday::where('id', $holiday['holiday_id'])->first();
                    if (!$exist_holiday) {
                        continue;
                    }
                    $userHoliday = new UserHoliday();
                    $userHoliday->user_id = $user->id;
                    $userHoliday->holiday_id = $exist_holiday->id;
                    $userHoliday->name = $holiday['name'] ?? null;
                    $userHoliday->date = $holiday['date'] ?? null;
                    $userHoliday->start_time = $holiday['start_time'] ?? null;
                    $userHoliday->end_time = $holiday['end_time'] ?? null;
                    if (isset($holiday['start_time'], $holiday['end_time'])) {
                        $userHoliday->is_full_day = 0;
                    } else {
                        $userHoliday->is_full_day = 1;
                    }
                    $userHoliday->is_custom = 0;
                    $userHoliday->save();
                }
            }
        }

        // Handle custom holidays
        $customHolidays = $request->custom_holidays ?? [];
        if (!empty($customHolidays)) {
            UserHoliday::where('user_id', $user->id)->where('is_custom', 1)->delete();
            foreach ($customHolidays as $customHoliday) {
                $rawDate = $customHoliday['date'] ?? null;

                if ($rawDate) {
                    try {
                        $formattedDate = Carbon::parse($rawDate)->format('Y-m-d');
                        $exists = UserHoliday::where('user_id', $user->id)
                            ->where('date', $formattedDate)
                            ->exists();

                        if (!$exists) {
                            $userHoliday = new UserHoliday();
                            $userHoliday->user_id = $user->id;
                            $userHoliday->name = $customHoliday['name'] ?? null;
                            $userHoliday->date = $formattedDate;
                            $userHoliday->start_time = $customHoliday['start_time'] ?? null;
                            $userHoliday->end_time = $customHoliday['end_time'] ?? null;
                            if (isset($customHoliday['start_time'], $customHoliday['end_time'])) {
                                $userHoliday->is_full_day = 0;
                            } else {
                                $userHoliday->is_full_day = 1;
                            }
                            $userHoliday->is_custom = 1;
                            $userHoliday->save();
                        }
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }
        }

        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Availability updated successfully!',
            'type' => 'success'
        ]);
    }

    public function saveIntroCards(Request $request)
    {
        $user = auth()->user();
        UserIntroCard::where('user_id', $user->id)->delete();
        foreach ($request->introCards as $introCard) {
            $userIntroCard = new UserIntroCard();
            $userIntroCard->user_id = $user->id;
            $userIntroCard->heading = $introCard['heading'] ?? null;
            $userIntroCard->description = $introCard['description'] ?? null;
            if (isset($introCard['image'])) {
                $userIntroCard->image = $this->storeImage('user-into-cards', $introCard['image']);
            } elseif (isset($introCard['old_image'])) {
                $userIntroCard->image = $introCard['old_image'];
            }
            $userIntroCard->save();
        }
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Intro cards updated successfully!',
            'type' => 'success'
        ]);
    }

    public function checkCurrentPassword(Request $request)
    {
        $user = auth()->user();
        $isValid = Hash::check($request->current_password, $user->password);

        return response()->json(['valid' => $isValid]);
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8',
            'confirm_password' => 'required|same:new_password',
        ]);

        $user = auth()->user();
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Current password is incorrect!',
                'type' => 'error'
            ]);
        }
        $user->password = Hash::make($request->new_password);
        $user->save();
        return redirect()->back()->with([
            'title' => 'Success',
            'message' => 'Password updated successfully!',
            'type' => 'success'
        ]);
    }

    public function showCustomer($id)
    {
        $user = User::where('ids', $id)->firstOrFail();
        return view('dashboard.admin.customers.show', compact('user'));
    }

    /**
     * AJAX method to filter and search customers
     */
    public function filterCustomers(Request $request)
    {
        // Only get values if they are actually provided and not empty
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $status = $request->filled('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $date = $request->filled('date') ? trim($request->get('date')) : '';
        $dateFilter = $request->filled('date_filter') ? $request->get('date_filter') : null;

        Log::info('Customer filter request', [
            'search' => $search,
            'status' => $status,
            'date' => $date,
            'dateFilter' => $dateFilter
        ]);

        // Base query for customers
        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        });

        // Only apply search if search term is provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Only apply status filter if status is provided and not 'all'
        if (!empty($status)) {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Only apply date filter if date information is provided
        if (!empty($dateFilter) && is_array($dateFilter)) {
            try {
                $startDate = $dateFilter['start_date'] ?? null;
                $endDate = $dateFilter['end_date'] ?? null;
                $filterType = $dateFilter['type'] ?? 'single';

                if (!empty($startDate)) {
                    $parsedStartDate = Carbon::parse($startDate)->format('Y-m-d');

                    if ($filterType === 'range' && !empty($endDate)) {
                        $parsedEndDate = Carbon::parse($endDate)->format('Y-m-d');
                        Log::info('Customer date range filter applied', [
                            'start_date' => $parsedStartDate,
                            'end_date' => $parsedEndDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', '>=', $parsedStartDate)
                            ->whereDate('created_at', '<=', $parsedEndDate);
                    } else {
                        Log::info('Customer single date filter applied', [
                            'date' => $parsedStartDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', $parsedStartDate);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Customer date filter parsing failed', ['date_filter' => $dateFilter, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        } elseif (!empty($date)) {
            // Fallback to old date handling for backward compatibility
            try {
                $filterDate = Carbon::parse($date)->format('Y-m-d');
                Log::info('Customer fallback date filter applied', ['original_date' => $date, 'parsed_date' => $filterDate]);
                $query->whereDate('created_at', $filterDate);
            } catch (\Exception $e) {
                Log::error('Customer fallback date parsing failed', ['date' => $date, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        $customers = $query->with(['profile', 'roles'])->paginate(10);
        $html = view('dashboard.admin.customers.partials.customers-table', ['customers' => $customers])->render();

        // Generate pagination HTML
        $paginationHtml = '';
        if ($customers->hasPages()) {
            $paginationHtml = $customers->links('pagination::bootstrap-4')->render();
        }

        Log::info('Customer filter results', [
            'count' => $customers->count(),
            'total' => $customers->total(),
            'has_pages' => $customers->hasPages(),
            'current_page' => $customers->currentPage(),
            'last_page' => $customers->lastPage(),
            'per_page' => $customers->perPage(),
            'pagination_html_length' => strlen($paginationHtml)
        ]);

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $customers->count(),
            'total' => $customers->total(),
            'has_pages' => $customers->hasPages()
        ]);
    }

    public function setting()
    {
        return view('dashboard.setting');
    }
}
