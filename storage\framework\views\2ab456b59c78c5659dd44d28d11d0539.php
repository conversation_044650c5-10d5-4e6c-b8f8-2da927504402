<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    <?php echo $__env->make('dashboard.admin.cms.include.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>

            <form action="<?php echo e(route('cms.privacy-terms.edit')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="type" value="privacy" />
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <!-- <div class="col-md-12 mb-5">
                                    <h3 class="section-heading mb-4"
                                        style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Privacy
                                        Policy</h3>
                                </div> -->

                            <div class="col-md-12">
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="blue-btn add-btn">Add Block</button>
                                </div>
                                <?php $__currentLoopData = $policies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <h5 class="mb-0">Section # <?php echo e($index + 1); ?></h5>
                                        <?php if(!$loop->first): ?>
                                            <button type="button" class="delete-block delete-block-btn">Delete this
                                                block</button>
                                        <?php endif; ?>
                                    </div>
                                    <div class="section-wrapper mb-10 card-box h-auto">
                                        <div class="mb-4">
                                            <label for="title" class="fieldlabels w-100">Title</label>
                                            <input type="text" class="form-control" name="privacy[<?php echo e($index); ?>][title]"
                                                placeholder="Enter privacy policy title" value="<?php echo e($policy->title ?? ''); ?>" />
                                        </div>

                                        <div class="mb-4">
                                            <label for="description" class="fieldlabels w-100">Content</label>
                                            <textarea class="form-control" name="privacy[<?php echo e($index); ?>][description]" rows="10"
                                                placeholder="Enter privacy policy content"><?php echo e($policy->description ?? ''); ?></textarea>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="col-md-12 ">
                                <button type="submit" class="blue-btn">Update Privacy Policy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            $(document).on('click', '.add-btn', function () {
                const section = $('.section-wrapper').length;

                const newSection = `
                <div class="section-wrapper p-4 mb-4" style="border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0" style="color: #555; font-weight: 600;">Section # ${section + 1}</h4>
                        <button type="button" class="btn btn-danger btn-sm delete-block-btn">Delete this block</button>
                    </div>

                    <div class="mb-4">
                        <label class="fieldlabels w-100">Title</label>
                        <input type="text" class="form-control" name="privacy[${section}][title]" placeholder="Enter privacy policy title" />
                    </div>

                    <div class="mb-4">
                        <label class="fieldlabels w-100">Content</label>
                        <textarea class="form-control" name="privacy[${section}][description]" rows="10" placeholder="Enter privacy policy content"></textarea>
                    </div>
                </div>
            `;

                $('.section-wrapper:last').after(newSection);
            });

            // Delete Block functionality
            $(document).on('click', '.delete-block-btn', function () {
                const totalSections = $('.section-wrapper').length;

                if (totalSections > 1) {
                    $(this).closest('.section-wrapper').remove();
                    updateSectionNumbers();
                } else {
                    alert('You must have at least one section.');
                }
            });

            // Update section numbers after deletion
            function updateSectionNumbers() {
                $('.section-wrapper').each(function (index) {
                    const sectionNumber = index + 1;
                    $(this).find('h4').text('Section # ' + sectionNumber);
                    $(this).find('input[name*="[title]"]').attr('name', `privacy[${index}][title]`);
                    $(this).find('textarea[name*="[description]"]').attr('name', `privacy[${index}][description]`);
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/privacy.blade.php ENDPATH**/ ?>