<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CmsPage;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class WebsiteController extends Controller
{
    function __construct()
    {
        $page = CmsPage::all();
        view()->share('pages', $page);
    }
    public function index()
    {
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        $services = Service::active()->get();
        return view('website.index', compact('page', 'categories'));
    }
    public function services($category = null, $subcategory = null)
    {
        $categories = Category::wherehas('subcategories')->active()->get();
        $active_category = $category;
        $active_subcategory = $subcategory;
        $services = Service::get();
        return view('website.service', compact('services', 'categories', 'active_category', 'active_subcategory'));
    }
    public function filterServices(Request $request)
    {
        // try {
        if (!$request->subcategory && !$request->category) {
            $categories = Category::wherehas('subcategories')->active()->get();
            $subcategories = $categories->first()->subcategories;
            $selectedSubcategory = $subcategories->first();
            $services = Service::get();
        } else {
            $categories = Category::with("subcategories", "services")->where("slug", $request->category)->active()->first();
            $subcategories = $categories->subcategories;
            if ($request->has('subcategory')) {
                $selectedSubcategory = $subcategories->where('slug', $request->subcategory)->first();
            } else {
                $selectedSubcategory = $subcategories->first();
            }
            $services = Service::where('category_id', $categories->id)->where('subcategory_id', $selectedSubcategory->id)->get();
        }

        $data = view('website.template.subcategory-service', compact('categories', 'subcategories', 'services', 'selectedSubcategory'))->render();
        return api_response(true, "Services", [
            'page' => $data,
            'category' => $request->category ?? '',
            'subcategory' => $selectedSubcategory->slug ?? ''
        ]);
        // } catch (\Exception $e) {
        //     return api_response(false, "Something went wrong");
        // }
    }
    public function professional($category = null, $subcategory = null)
    {
        $categories = Category::wherehas('subcategories')->active()->get();
        $active_category = $category;
        $active_subcategory = $subcategory;
        $professionals = User::professional()->get();
        return view('website.professional', compact('professionals', 'categories', 'active_category', 'active_subcategory'));
    }

    public function filterProfessional(Request $request)
    {
        try {
            if (!$request->subcategory && !$request->category) {
                $categories = Category::wherehas('subcategories')->active()->get();
                $subcategories = $categories->first()->subcategories;
                $professionals = User::professional()->get();
            } else {
                $categories = Category::with("subcategories", "services")->where("slug", $request->category)->active()->first();
                $subcategories = $categories->subcategories;
                if ($request->has('subcategory')) {
                    $selectedSubcategory = $subcategories->where('slug', $request->subcategory)->first();
                } else {
                    $selectedSubcategory = $subcategories->first();
                }
                $professionals = $selectedSubcategory->users;
            }
            $data = view('website.template.subcategory-professional', compact('categories', 'subcategories', 'professionals'))->render();
            return api_response(true, "Professionals", [
                'page' => $data,
                'category' => $request->category??'',
                'subcategory' => $selectedSubcategory->slug??''
            ]);
        } catch (\Exception $e) {
            return api_response(false, "Something went wrong");
        }
    }
    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }

    public function showPage($slug)
    {
        $page = CmsPage::where('slug', $slug)->first();
        if (!$page) {
            return redirect()->route('home');
        }
        return view('website.show', compact('page'));
    }
}
