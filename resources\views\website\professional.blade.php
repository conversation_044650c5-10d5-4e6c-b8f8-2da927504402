@extends('website.layout.master')

@push('css')
    <style>
        .professional-swipper .swiper-slide {
            width: auto !important;
            flex-shrink: 0;
        }

        .professional-swipper .nav-item {
            margin: 0;
        }

        .professional-swipper .professional-tab {
            white-space: nowrap;
            min-width: fit-content;
        }
    </style>
@endpush

@section('content')
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h1 class="sora black  fs-34 semi_bold">Professional</h1>

                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input type="text" placeholder="Search">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">

                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper">
                                <div class="swiper-wrapper">
                                    @foreach ($categories as $loop_index => $category)
                                        <div class="swiper-slide">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link {{ $category->slug == $active_category ? 'active' : '' }} professional-tab category-tab-btn"
                                                    id="pills-category-{{ $category->slug }}-tab"
                                                    data-category-name="{{ $category->slug }}" data-bs-toggle="pill"
                                                    data-bs-target="#pills-profile" type="button" role="tab"
                                                    aria-controls="pills-profile"
                                                    aria-selected="{{ $category->slug == $active_category ? 'true' : 'false' }}">
                                                    {{ $category->name }}
                                                </button>
                                            </li>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>
                    <div id="subcategory-professional-container"></div>
            
                    {{-- <div class="tab-content mt-10" id="pills-tabContent  ">
                        @php
                        $images = [
                        'card-image.png',
                        'professional1.png',
                        'professional2.png',
                        'card-image.png',
                        'professional1.png',
                        'professional2.png',
                        'card-image.png',
                        'professional1.png',
                        'professional2.png',
                        'card-image.png',
                        'professional1.png',
                        'professional2.png',
                        ];
                        @endphp
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel"
                            aria-labelledby="pills-home-tab" tabindex="0">
                            <ul class="nav nav-pills mb-10  service-subcategory" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active service-tab" id="pills-fitness-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-fitness" type="button" role="tab"
                                        aria-controls="pills-fitness" aria-selected="true">General Fitness
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link  service-tab" id="pills-strengthconditioning-tab"
                                        data-bs-toggle="pill" data-bs-target="#pills-strengthconditioning" type="button"
                                        role="tab" aria-controls="pills-strengthconditioning" aria-selected="true">
                                        Strength/Conditioning
                                    </button>
                                </li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center mb-10 mt-15">
                                <p class="m-0 fs-14 light-black">25 Personal Trainers near you</p>
                                <a href="" class="fs-14 sora normal black" data-bs-toggle="modal"
                                    data-bs-target="#filterModal">
                                    <div class="filter-select d-flex gap-2 align-items-center">
                                        @include('svg.filter')
                                        <span>Filter</span>
                                    </div>
                                </a>
                            </div>
                            <div class="tab-content mt-10" id="pills-tabContent  ">
                                <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                                    aria-labelledby="pills-fitness-tab" tabindex="0">
                                    <div class="row row-gap-8">
                                        @auth
                                        @include('website.template.top-rated')
                                        @else
                                        @for ($i = 0; $i < 4; $i++) <div class="col-lg-3 col-sm-6 col-12">
                                            <div class="card top-rated-card">
                                                <div class="card-header border-0 p-0 position-relative">
                                                    <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                                        class="h-100 w-100 top-rated-image" alt="card-image">
                                                    <div class="fav-icon position-absolute  bottom-10 ">
                                                        <i class="fa-regular fa-heart"></i>
                                                        <input type="hidden" name="wishlist_product_id" value="123">
                                                    </div>
                                                    <div class="rated-div position-absolute">
                                                        <p class="fs-12 sora semi_bold m-0">
                                                            @include('svg.rated')TOP RATED
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="card-body pb-0 p-5">
                                                    <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                    <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                            class="fa-solid fa-star review-icon mx-1"></i> <span
                                                            class="normal">(440)</span></p>
                                                    <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                                </div>
                                                <div class="card-footer border-0 pt-0 p-5">
                                                    <span class="badge white-badge">Beauty Salon</span>
                                                </div>
                                            </div>
                                    </div>
                                    @endfor

                                    @endauth
                                    @for ($i = 0; $i < 8; $i++) <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                    <i class="fa-regular fa-heart"></i>
                                                    <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>

                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge">Beauty Salon</span>
                                            </div>
                                        </div>
                                </div>
                                @endfor
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-strengthconditioning" role="tabpanel"
                            aria-labelledby="pills-strengthconditioning-tab" tabindex="0">
                            <div class="row row-gap-8">
                                @auth
                                @include('website.template.top-rated')
                                @else
                                @for ($i = 0; $i < 4; $i++) <div class="col-lg-3 col-sm-6 col-12">
                                    <div class="card top-rated-card">
                                        <div class="card-header border-0 p-0 position-relative">
                                            <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                                class="h-100 w-100 top-rated-image" alt="card-image">
                                            <div class="fav-icon position-absolute  bottom-10 ">
                                                <i class="fa-regular fa-heart"></i>
                                                <input type="hidden" name="wishlist_product_id" value="123">
                                            </div>
                                            <div class="rated-div position-absolute">
                                                <p class="fs-12 sora semi_bold m-0">
                                                    @include('svg.rated')TOP RATED
                                                </p>
                                            </div>
                                        </div>
                                        <div class="card-body pb-0 p-5">
                                            <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                            <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                    class="fa-solid fa-star review-icon mx-1"></i> <span
                                                    class="normal">(440)</span></p>
                                            <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                        </div>
                                        <div class="card-footer border-0 pt-0 p-5">
                                            <span class="badge white-badge">Beauty Salon</span>
                                        </div>
                                    </div>
                            </div>
                            @endfor

                            @endauth
                            @for ($i = 0; $i < 8; $i++) <div class="col-lg-3 col-sm-6 col-12">
                                <div class="card top-rated-card">
                                    <div class="card-header border-0 p-0 position-relative">
                                        <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                            class="h-100 w-100 top-rated-image" alt="card-image">
                                        <div class="fav-icon position-absolute  bottom-10 ">
                                            <i class="fa-regular fa-heart"></i>
                                            <input type="hidden" name="wishlist_product_id" value="123">
                                        </div>

                                    </div>
                                    <div class="card-body pb-0 p-5">
                                        <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                        <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                class="fa-solid fa-star review-icon mx-1"></i> <span
                                                class="normal">(440)</span></p>
                                        <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                    </div>
                                    <div class="card-footer border-0 pt-0 p-5">
                                        <span class="badge white-badge">Beauty Salon</span>
                                    </div>
                                </div>
                        </div>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
            <div class="row row-gap-8">
                @for ($i = 0; $i < 4; $i++) <div class="col-lg-3 col-sm-6 col-12">
                    <div class="card top-rated-card">
                        <div class="card-header border-0 p-0 position-relative">
                            <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                class="h-100 w-100 top-rated-image" alt="card-image">
                            <div class="fav-icon position-absolute  bottom-10 ">
                                <i class="fa-regular fa-heart"></i>
                                <input type="hidden" name="wishlist_product_id" value="123">
                            </div>
                            <div class="rated-div position-absolute">
                                <p class="fs-12 sora semi_bold m-0">@include('svg.rated')TOP
                                    RATED
                                </p>
                            </div>
                        </div>
                        <div class="card-body pb-0 p-5">
                            <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                            <p class="fs-15 sora bold m-0 light-black">4.5 <i class="fa-solid fa-star review-icon mx-1"></i>
                                <span class="normal">(440)</span>
                            </p>
                            <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                        </div>
                        <div class="card-footer border-0 pt-0 p-5">
                            <span class="badge white-badge">Beauty Salon</span>
                        </div>
                    </div>
            </div>
            @endfor
            @for ($i = 0; $i < 8; $i++) <div class="col-lg-3 col-sm-6 col-12">
                <div class="card top-rated-card">
                    <div class="card-header border-0 p-0 position-relative">
                        <img src="{{ asset('website/assets/images/' . $images[$i]) }}" class="h-100 w-100 top-rated-image"
                            alt="card-image">
                        <div class="fav-icon position-absolute  bottom-10 ">
                            <i class="fa-regular fa-heart"></i>
                            <input type="hidden" name="wishlist_product_id" value="123">
                        </div>

                    </div>
                    <div class="card-body pb-0 p-5">
                        <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                        <p class="fs-15 sora bold m-0 light-black">4.5 <i class="fa-solid fa-star review-icon mx-1"></i>
                            <span class="normal">(440)</span>
                        </p>
                        <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                    </div>
                    <div class="card-footer border-0 pt-0 p-5">
                        <span class="badge white-badge">Beauty Salon</span>
                    </div>
                </div>
        </div>
        @endfor
        </div>
        </div>
        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab" tabindex="0">
            <div class="row row-gap-8">
                @for ($i = 0; $i < 4; $i++) <div class="col-lg-3 col-sm-6 col-12">
                    <div class="card top-rated-card">
                        <div class="card-header border-0 p-0 position-relative">
                            <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                class="h-100 w-100 top-rated-image" alt="card-image">
                            <div class="fav-icon position-absolute  bottom-10 ">
                                <i class="fa-regular fa-heart"></i>
                                <input type="hidden" name="wishlist_product_id" value="123">
                            </div>
                            <div class="rated-div position-absolute">
                                <p class="fs-12 sora semi_bold m-0">@include('svg.rated')TOP
                                    RATED
                                </p>
                            </div>
                        </div>
                        <div class="card-body pb-0 p-5">
                            <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                            <p class="fs-15 sora bold m-0 light-black">4.5 <i class="fa-solid fa-star review-icon mx-1"></i>
                                <span class="normal">(440)</span>
                            </p>
                            <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                        </div>
                        <div class="card-footer border-0 pt-0 p-5">
                            <span class="badge white-badge">Beauty Salon</span>
                        </div>
                    </div>
            </div>
            @endfor
            @for ($i = 0; $i < 8; $i++) <div class="col-lg-3 col-sm-6 col-12">
                <div class="card top-rated-card">
                    <div class="card-header border-0 p-0 position-relative">
                        <img src="{{ asset('website/assets/images/' . $images[$i]) }}" class="h-100 w-100 top-rated-image"
                            alt="card-image">
                        <div class="fav-icon position-absolute  bottom-10 ">
                            <i class="fa-regular fa-heart"></i>
                            <input type="hidden" name="wishlist_product_id" value="123">
                        </div>

                    </div>
                    <div class="card-body pb-0 p-5">
                        <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                        <p class="fs-15 sora bold m-0 light-black">4.5 <i class="fa-solid fa-star review-icon mx-1"></i>
                            <span class="normal">(440)</span>
                        </p>
                        <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                    </div>
                    <div class="card-footer border-0 pt-0 p-5">
                        <span class="badge white-badge">Beauty Salon</span>
                    </div>
                </div>
        </div>
        @endfor
        </div>
        </div>
        </div> --}}
        </div>
        </div>
        </div>

    </section>
    @include('website.template.modal.filter-modal')
@endsection
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script>
        function swiperScript() {
            if ($(".top-rated-swiper").length) {
                var topRatedSwiper = new Swiper(".top-rated-swiper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 30,
                    navigation: {
                        nextEl: ".top-rated-next",
                        prevEl: ".top-rated-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                    },
                    // breakpoints: {
                    //     320: {
                    //         slidesPerView: 1,
                    //         spaceBetween: 20
                    //     },
                    //     768: {
                    //         slidesPerView: 2,
                    //         spaceBetween: 30
                    //     },
                    //     1024: {
                    //         slidesPerView: 3,
                    //         spaceBetween: 30
                    //     }
                    // }
                });
            }
            else {
                alert('not exists')
            }
        }
        const getProfessional = (category, subcategory) => {
            $.ajax({
                url: "{{ route('filter_professional') }}",
                type: "GET",
                data: {
                    category: category,
                    subcategory: subcategory
                },
                success: function (response) {
                    if (response.status === true) {
                        $('#subcategory-professional-container').html(response.data.page);

                        // Update the URL without reloading
                        let newUrl = `/professional/${category}`;
                        if (response.data.subcategory) {
                            newUrl += `/${response.data.subcategory}`;
                        }
                        window.history.pushState({ path: newUrl }, '', newUrl);
                        swiperScript();
                    } else {
                        $('#subcategory-professional-container').html(response.message);
                    }
                },
                error: function (xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    $('#subcategory-professional-container').html("An error occurred. Please try again.");
                }
            });
        }

        $(document).ready(function () {
            // Initialize Professional Swiper
            var professionalSwiper = new Swiper(".professional-swipper", {
                slidesPerView: '4',
                spaceBetween: 10,
                freeMode: true,
                navigation: {
                    nextEl: ".professional-next",
                    prevEl: ".professional-prev",
                },
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 5
                    },
                    640: {
                        slidesPerView: 3,
                        spaceBetween: 10
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 10
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 10
                    }
                }
            });

            // Initialize Top Rated Swiper if exists

            // Fetch default professionals on load
            getProfessional('{{ $active_category }}', '{{ $active_subcategory }}');

            // Handle tab button clicks
            $(document).on('click', '.category-tab-btn, .subcategory-tab-btn', function () {
                let category = $(this).data("category-name");
                let subcategory = $(this).data("subcategory-name");
                getProfessional(category, subcategory);
            });
        });
    </script>
@endpush