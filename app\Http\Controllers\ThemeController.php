<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class ThemeController extends Controller
{
    function __construct()
    {

    }
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function professional_account()
    {
        return view('auth.professional_account_stepper');
    }

    public function dashboard(){
        return view('dashboard.index');
    }
    public function testing()
    {
        return view('dashboard.customer.testing');
    }
    public function familyFriends()
    {
        return view('dashboard.customer.family-friends');
    }
    public function addFriends()
    {
        return view('dashboard.customer.add-friend');
    }
    public function friendsDetails()
    {
        return view('dashboard.customer.friends-family-detail');
    }
    public function customerBooking()
    {
        return view('dashboard.customer.customer-booking');
    }
    public function customerWallet()
    {
        return view('dashboard.customer.customer-wallet');
    }
    public function favoriteProfessional()
    {
        return view('dashboard.customer.favorite_professional');
    }
    public function cart()
    {
        return view('dashboard.customer.cart');
    }

    public function professional_profile()
    {
        return view('dashboard.customer.professional_profile');
    }

    public function notification()
    {
        $notifications = auth()->user()->notifications()->latest()->get();
        return view('dashboard.notification', compact('notifications'));
    }
    //    business controller
    public function businessAnalytics()
    {
        return view('dashboard.business.analytics');
    }
    public function businessBooking()
    {
        return view('dashboard.business.business-booking');
    }
    public function businessEarning()
    {
        return view('dashboard.business.earning');
    }

    public function staffMemberDetails()
    {
        return view('dashboard.business.staff-member-details');
    }
    // Individual

    // admin

    public function refundRequest()
    {
        return view('dashboard.admin.refund-request');
    }
    public function adminWallet()
    {
        return view('dashboard.admin.wallet');
    }
    public function adminVat()
    {
        return view('dashboard.admin.vat-mgmt');
    }
}
