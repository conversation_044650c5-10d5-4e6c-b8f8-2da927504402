<div class="cms_tabs_wapper p-4 rounded-3">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a href="<?php echo e(route('cms.home')); ?>"
                class="nav-link fs-14 normal sora semi_bold header-active <?php echo e(request()->routeIs('cms.home') ? 'active' : ''); ?> btn_transparent_border_blue">Home</a>
        </li>

        <li class="nav-item" role="presentation">
            <a href="<?php echo e(route('cms.privacy')); ?>"
                class="nav-link fs-14 normal sora semi_bold header-active <?php echo e(request()->routeIs('cms.privacy') ? 'active' : ''); ?> btn_transparent_border_blue">Privacy
                Policy</a>
        </li>

        <li class="nav-item" role="presentation">
            <a href="<?php echo e(route('cms.terms')); ?>"
                class="nav-link fs-14 normal sora semi_bold header-active <?php echo e(request()->routeIs('cms.terms') ? 'active' : ''); ?> btn_transparent_border_blue">Terms</a>
        </li>
        <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <a href="<?php echo e(route('page.show', $page->slug)); ?>"
                    class="nav-link fs-14 normal sora semi_bold header-active <?php echo e(request()->routeIs('page.show') ? 'active' : ''); ?> btn_transparent_border_blue"><?php echo e($page->title ?? ''); ?></a>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <div class="d-flex justify-content-end w-75">
            <a class="btn add-btn" href="<?php echo e(route('cms.create')); ?>">
                + Add a new page
            </a>
        </div>
    </ul>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/include/navbar.blade.php ENDPATH**/ ?>