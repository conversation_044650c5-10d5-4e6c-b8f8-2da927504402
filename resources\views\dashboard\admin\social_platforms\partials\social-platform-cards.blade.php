@foreach ($platforms as $platform)
    <div class="col-lg-6">
        <div class="card flex-row justify-content-center align-items-center p-3 gap-5">
            <div class="card-header border-0 p-0 justify-content-center align-items-center">
                <img src="{{ asset("website").'/'.$platform->image }}"
                    class="h-35px w-35px  object-fit-contain top-rated-image" alt="card-image">
            </div>
            <div class="card-body p-0">
                <p class="fs-16 sora w-700 m-0 dark-blue">{{ $platform->name ?? '' }}</p>
            </div>
            <div class="card-footer p-0 border-0">
                <div class="dropdown">
                    <a class="drop-btn" type="button" id="dropdownMenuButton"
                        data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        @can('social-platforms-edit')
                            <li>
                                <button class="dropdown-item complete fs-14 regular edit-social-platform"
                                    type="button" data-id="{{ $platform->id }}">
                                    <i class="bi bi-check-circle complete-icon"></i>
                                    Edit
                                </button>
                            </li>
                        @endcan
                        @can('social-platforms-delete')
                            <li>
                                <form action="{{ route('social-platforms.destroy', $platform->id) }}"
                                    method="POST" class="delete-form">
                                    @csrf
                                    @method('DELETE')
                                    <button class="dropdown-item cancel fs-14 regular"
                                        type="button" onclick="showDeleteConfirmation(this)">
                                        <i class="fa-solid fa-xmark cancel-icon"></i>
                                        Delete
                                    </button>
                                </form>
                            </li>
                        @endcan
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endforeach
