<?php

namespace App\Http\Controllers;

use App\Models\SocialPlatform;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class SocialPlatformController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $offset = $request->get('offset', 0);
        $limit = 10;
        $search = $request->get('search');

        $query = SocialPlatform::query();
        if ($search) {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }
        $totalCount = $query->count();
        $platforms = $query->offset($offset)->limit($limit)->get();

        if ($request->ajax()) {
            $html = view('dashboard.admin.social_platforms.partials.social-platform-cards', [
                'platforms' => $platforms
            ])->render();

            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $platforms->count(),
                'total' => $totalCount,
                'offset' => $offset,
                'next_offset' => $offset + $platforms->count(),
                'has_more' => ($offset + $limit) < $totalCount
            ]);
        }
        return view('dashboard.admin.social_platforms.index', [
            'platforms' => $platforms,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create() {}

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:5120',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $data = $validator->validated();
            if ($request->hasFile('avatar')) {
                $data['image'] = $this->storeImage('social-platforms', $request->file('avatar'));
                unset($data['avatar']);
            }
            SocialPlatform::create($data);
            DB::commit();
            return redirect()->back()->with(['type' => 'success', 'message' => 'Social Platform created successfully', 'title' => 'Created']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $platform = SocialPlatform::find($id);
        if (!$platform) {
            return response()->json(['error' => 'Social Platform not found'], 404);
        }
        return response()->json($platform);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:5120',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ], 422);
        }
        try {
            DB::beginTransaction();
            $platform = SocialPlatform::find($id);
            if (!$platform) {
                return response()->json(['type' => 'error', 'message' => 'Social Platform not found', 'title' => 'Error'], 404);
            }

            $data = $validator->validated();
            if ($request->hasFile('avatar')) {
                if ($platform->image) {
                    $this->deleteImage($platform->image);
                }
                $data['image'] = $this->storeImage('social-platforms', $request->file('avatar'));
                unset($data['avatar']);
            } else {
                unset($data['avatar']);
            }

            $platform->update($data);
            DB::commit();
            return response()->json(['type' => 'success', 'message' => 'Social Platform updated successfully', 'title' => 'Updated']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $platform = SocialPlatform::find($id);
            if (!$platform) {
                return redirect()->back()->with(['type' => 'error', 'message' => 'Social Platform not found', 'title' => 'Error']);
            }

            if ($platform->image) {
                $this->deleteImage($platform->image);
            }
            $platform->delete();
            return redirect()->back()->with(['type' => 'success', 'message' => 'Social Platform deleted successfully', 'title' => 'Deleted']);
        } catch (\Throwable $th) {
            return redirect()->back()->with(['type' => 'error', 'message' => $th->getMessage(), 'title' => 'Error']);
        }
    }
}
